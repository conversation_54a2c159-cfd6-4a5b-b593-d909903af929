{"_args": [["@types/istanbul-lib-coverage@2.0.3", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/istanbul-lib-coverage@2.0.3", "_id": "@types/istanbul-lib-coverage@2.0.3", "_inBundle": false, "_integrity": "sha1-S6jdtyAiH0MuRDvV+RF/0iz9R2I=", "_location": "/@types/istanbul-lib-coverage", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/istanbul-lib-coverage@2.0.3", "name": "@types/istanbul-lib-coverage", "escapedName": "@types%2fistanbul-lib-coverage", "scope": "@types", "rawSpec": "2.0.3", "saveSpec": null, "fetchSpec": "2.0.3"}, "_requiredBy": ["/@jest/test-result", "/@jest/types", "/@types/istanbul-lib-report", "/v8-to-istanbul"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.3.tgz", "_spec": "2.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/jason0x43"}, {"name": "<PERSON>", "url": "https://github.com/loryman"}], "dependencies": {}, "description": "TypeScript definitions for istanbul-lib-coverage", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/istanbul-lib-coverage", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/istanbul-lib-coverage"}, "scripts": {}, "typeScriptVersion": "3.0", "types": "index.d.ts", "typesPublisherContentHash": "a951ff253666ffd402e5ddf6b7d5a359e22c9a6574f6a799a39e1e793107b647", "version": "2.0.3"}