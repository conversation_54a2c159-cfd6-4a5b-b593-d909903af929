{"_args": [["@types/webpack@4.41.32", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/webpack@4.41.32", "_id": "@types/webpack@4.41.32", "_inBundle": false, "_integrity": "sha1-p7qwO3KQQHAWKy8WlBVJIgnpQhI=", "_location": "/@types/webpack", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/webpack@4.41.32", "name": "@types/webpack", "escapedName": "@types%2fwebpack", "scope": "@types", "rawSpec": "4.41.32", "saveSpec": null, "fetchSpec": "4.41.32"}, "_requiredBy": ["/clean-webpack-plugin", "/html-webpack-plugin"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/webpack/-/webpack-4.41.32.tgz", "_spec": "4.41.32", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/tkqubo"}, {"name": "<PERSON>", "url": "https://github.com/bumbleblym"}, {"name": "<PERSON>", "url": "https://github.com/bcherny"}, {"name": "<PERSON>", "url": "https://github.com/tommy<PERSON>ylin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1"}, {"name": "<PERSON>", "url": "https://github.com/jcreamer898"}, {"name": "<PERSON>", "url": "https://github.com/alan-agius4"}, {"name": "<PERSON>", "url": "https://github.com/dennispg"}, {"name": "<PERSON>", "url": "https://github.com/christo<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ZSkycat", "url": "https://github.com/ZSkycat"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON>", "url": "https://github.com/rwaskie<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/grgur"}, {"name": "<PERSON><PERSON><PERSON> Gonçalves Cavalcante", "url": "https://github.com/rubenspgcavalcante"}, {"name": "<PERSON>", "url": "https://github.com/andersk"}, {"name": "<PERSON>", "url": "https://github.com/ofhouse"}, {"name": "<PERSON>", "url": "https://github.com/danielthank"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/sasurau4"}, {"name": "<PERSON>", "url": "https://github.com/dionshihk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/spamshaker"}], "dependencies": {"@types/node": "*", "@types/tapable": "^1", "@types/uglify-js": "*", "@types/webpack-sources": "*", "anymatch": "^3.0.0", "source-map": "^0.6.0"}, "description": "TypeScript definitions for webpack", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack", "license": "MIT", "main": "", "name": "@types/webpack", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack"}, "scripts": {}, "typeScriptVersion": "3.7", "types": "index.d.ts", "typesPublisherContentHash": "52004f2c8cc8279cf14bf5026b269127c3cf71ba96929f7bdb41fe1de457d1ba", "version": "4.41.32"}