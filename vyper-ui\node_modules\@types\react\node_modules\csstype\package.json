{"_args": [["csstype@3.0.10", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "csstype@3.0.10", "_id": "csstype@3.0.10", "_inBundle": false, "_integrity": "sha1-KtOnvtcPNbllcHwJLl8wsyfCkOU=", "_location": "/@types/react/csstype", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "csstype@3.0.10", "name": "csstype", "escapedName": "csstype", "rawSpec": "3.0.10", "saveSpec": null, "fetchSpec": "3.0.10"}, "_requiredBy": ["/@types/react"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/csstype/-/csstype-3.0.10.tgz", "_spec": "3.0.10", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/frenic/csstype/issues"}, "description": "Strict TypeScript and Flow types for style based on MDN data", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^1.0.6", "@types/jest": "^27.0.1", "@types/jsdom": "^16.2.13", "@types/node": "^16.9.1", "@types/prettier": "^2.3.2", "@types/request": "^2.48.7", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^4.31.0", "@typescript-eslint/parser": "^4.31.0", "chalk": "^4.1.2", "chokidar": "^3.5.2", "css-tree": "^1.1.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "fast-glob": "^3.2.7", "flow-bin": "^0.159.0", "jest": "^27.2.0", "jsdom": "^17.0.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#a9a17ff717b73cb9bb7072357a080509b73e22bb", "mdn-data": "git+https://github.com/mdn/data.git#ff55c39c1da3f1519e3a8f890a7cf6e6339a4b87", "prettier": "^2.4.0", "request": "^2.88.2", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "turndown": "^7.1.1", "typescript": "~4.4.3"}, "files": ["index.d.ts", "index.js.flow"], "homepage": "https://github.com/frenic/csstype#readme", "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"], "license": "MIT", "main": "", "name": "csstype", "repository": {"type": "git", "url": "git+https://github.com/frenic/csstype.git"}, "scripts": {"build": "ts-node --files build.ts --start", "lazy": "tsc && npm run lint", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "test": "jest", "test:dist": "jest dist.*.ts", "test:src": "jest src.*.ts", "update": "ts-node --files update.ts", "watch": "ts-node --files build.ts --watch"}, "types": "index.d.ts", "version": "3.0.10"}