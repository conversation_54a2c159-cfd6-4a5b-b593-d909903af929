{"_args": [["clsx@1.1.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "clsx@1.1.1", "_id": "clsx@1.1.1", "_inBundle": false, "_integrity": "sha1-mLMTT5q73yOyZjSRrOE8XAOnMYg=", "_location": "/clsx", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "clsx@1.1.1", "name": "clsx", "escapedName": "clsx", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/", "/@material-ui/core", "/@material-ui/lab", "/@material-ui/pickers", "/@material-ui/styles", "/material-table/@material-ui/pickers", "/notistack"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/clsx/-/clsx-1.1.1.tgz", "_spec": "1.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "bugs": {"url": "https://github.com/lukeed/clsx/issues"}, "description": "A tiny (228B) utility for constructing className strings conditionally.", "devDependencies": {"bundt": "1.0.1", "esm": "3.2.25", "tap-spec": "5.0.0", "tape": "4.9.1"}, "engines": {"node": ">=6"}, "files": ["*.d.ts", "dist"], "homepage": "https://github.com/lukeed/clsx#readme", "keywords": ["classes", "classname", "classnames"], "license": "MIT", "main": "dist/clsx.js", "module": "dist/clsx.m.js", "name": "clsx", "repository": {"type": "git", "url": "git+https://github.com/lukeed/clsx.git"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "tape -r esm test/*.js | tap-spec"}, "types": "clsx.d.ts", "unpkg": "dist/clsx.min.js", "version": "1.1.1"}