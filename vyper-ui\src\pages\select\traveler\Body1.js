import React from "react";
import { Operation } from "./Operation";
import { AddRequiredButton } from "./buttons/AddRequiredButton";
import { Traveler } from './Traveler';

export const Body = ({
  vyper,
  build,
  options,
  onEditOperation,
  onAddOperation,
  onRemoveOperation,
  onEditComponentName,
  onEditComponentValue,
  onAddComponent,
  onRemoveComponent,
  onClickValidate,
  onCommentOperation,
  approvedGroups,
  onUndoDelete,
  onSelectDiagramApproval,
  unApprovedGroups,
}) => {
  const determineValidatedOperation = (operation) => {
    return build.validatedOperations.find(
      (vo) => vo.operation === operation.name
    );
  };

  const operations = build.traveler.operations.filter(
    (operation) => operation.name !== "TEST"
  );

  const isDevicePkgNiche = build?.templateSource?.templateType === 'DEVICE_PKGNICHE' || build?.templateSource?.templateType == undefined


  const handleSectionClick = (sectionName) => {
    const sectionElement = document.getElementById(sectionName);
    if (sectionElement) {
      const rect = sectionElement.getBoundingClientRect();
      const offset = rect.top + window.scrollY - document.querySelector('.optionBar').offsetHeight - 50;
      window.scrollTo({ top: offset, behavior: "smooth" });
    }
  };


return (
  <div style={{ display: 'flex', flexDirection: 'row' }}>
     <div style={{
      width: '250px',
      height: '50vh',
      position: 'sticky',
      fontSize: '0.8rem',
      top: isDevicePkgNiche ? '300px' : '375px',
      left: '0',
      zIndex: 1,
      overflowY: 'auto',
      border: '1px solid #ccc',
      borderBottom: '1 px solid #ccc',
      padding: '20px',
      paddingBottom: '10%',
      boxSizing: 'border-box',
      flexShrink: 0,
    } }>
      <h2>Operations</h2>
      <ul>
        {operations.map((operation, index) => (
          <li key={index}>
            <a style={{ color: "blue" }}
              href={`#operation-${index}`}
              onClick={() => handleSectionClick(`operation-${index}`)} 
            >
              {operation.name}
            </a>
          </li>
        ))}
      </ul>
    </div>
    <div style={{ paddingLeft: '40px', flex: 1, minWidth: '0', overflowX: 'auto' }}>
      <Traveler vyper={vyper} build={build} options={options} />
      {options.editbutton ? (
        <AddRequiredButton
          title="Add Operation"
          onClick={() => onAddOperation(null)}
        />
      ) : null}

      {operations.map((operation, index) => (
        <div id={`operation-${index}`} key={index} style={{ scrollMarginTop: isDevicePkgNiche ? '256px' : '327px', }}>
          <Operation 
            vyper={vyper}
            build={build}
            operation={operation}
            operationIndex={index}
            options={options}
            onEditOperation={(o) => onEditOperation(o, index)}
            onAddOperation={(o) => onAddOperation(o, index)}
            onRemoveOperation={(o) => onRemoveOperation(o, index)}
            onEditComponentName={(o, c, cPos) =>
              onEditComponentName(o, c, index, cPos)
            }
            onEditComponentValue={(o, c, cPos) =>
              onEditComponentValue(o, c, index, cPos)
            }
            onAddComponent={(o, c, cPos) => onAddComponent(o, c, index, cPos)}
            onRemoveComponent={(o, c, cPos) => onRemoveComponent(o, c, index, cPos)}
            validatedOperation={determineValidatedOperation(operation)}
            onClickValidate={onClickValidate}
            onCommentOperation={onCommentOperation}
            approvedGroups={approvedGroups}
            onUndoDelete={(o) => onUndoDelete(o)}
            reworkedTraveler={build?.reworkedTraveler}
            onSelectDiagramApproval={onSelectDiagramApproval}
            unApprovedGroups={unApprovedGroups}
          />
        </div>

      ))}
    </div>
  </div>
)


};