{"_args": [["@types/json-schema@7.0.9", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/json-schema@7.0.9", "_id": "@types/json-schema@7.0.9", "_inBundle": false, "_integrity": "sha1-l+3JA36gw4WFMgsolk3eOznkZg0=", "_location": "/@types/json-schema", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/json-schema@7.0.9", "name": "@types/json-schema", "escapedName": "@types%2fjson-schema", "scope": "@types", "rawSpec": "7.0.9", "saveSpec": null, "fetchSpec": "7.0.9"}, "_requiredBy": ["/babel-loader/schema-utils", "/css-loader/schema-utils", "/schema-utils", "/style-loader/schema-utils"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/json-schema/-/json-schema-7.0.9.tgz", "_spec": "7.0.9", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/bcherny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/cyrilletuzi"}, {"name": "<PERSON>", "url": "https://github.com/lucianbuzzo"}, {"name": "<PERSON>", "url": "https://github.com/rolandjitsu"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK"}], "dependencies": {}, "description": "TypeScript definitions for json-schema 4.0, 6.0 and", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/json-schema", "license": "MIT", "main": "", "name": "@types/json-schema", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/json-schema"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "2eb4af77c88f8be05a313707b99902ae4fc14609b7a2af2b5ce7007443a63253", "version": "7.0.9"}