{"_args": [["@types/babel__core@7.1.17", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/babel__core@7.1.17", "_id": "@types/babel__core@7.1.17", "_inBundle": false, "_integrity": "sha1-9QrJ0g1kFTtRBXjYT5ZD+aOvvmQ=", "_location": "/@types/babel__core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/babel__core@7.1.17", "name": "@types/babel__core", "escapedName": "@types%2fbabel__core", "scope": "@types", "rawSpec": "7.1.17", "saveSpec": null, "fetchSpec": "7.1.17"}, "_requiredBy": ["/babel-jest", "/babel-plugin-jest-hoist"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/babel__core/-/babel__core-7.1.17.tgz", "_spec": "7.1.17", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "url": "https://github.com/marvinhagemeister"}, {"name": "<PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia"}, {"name": "<PERSON><PERSON><PERSON> Jr.", "url": "https://github.com/ifiokjr"}], "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}, "description": "TypeScript definitions for @babel/core", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "license": "MIT", "main": "", "name": "@types/babel__core", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__core"}, "scripts": {}, "typeScriptVersion": "3.8", "types": "index.d.ts", "typesPublisherContentHash": "503ee27ea45ac43a667cde36107fe4242d077ac2a3cd9c1cbb3b8bbf58fce397", "version": "7.1.17"}