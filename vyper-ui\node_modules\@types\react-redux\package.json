{"_from": "@types/react-redux@^7.1.20", "_id": "@types/react-redux@7.1.34", "_inBundle": false, "_integrity": "sha512-GdFaVjEbYv4Fthm2ZLvj1VSCedV7TqE5y1kNwnjSdBOTXuRSgowux6J8TAct15T3CKBr63UMk+2CO7ilRhyrAQ==", "_location": "/@types/react-redux", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/react-redux@^7.1.20", "name": "@types/react-redux", "escapedName": "@types%2freact-redux", "scope": "@types", "rawSpec": "^7.1.20", "saveSpec": null, "fetchSpec": "^7.1.20"}, "_requiredBy": ["/react-redux"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/react-redux/-/react-redux-7.1.34.tgz", "_shasum": "83613e1957c481521e6776beeac4fd506d11bd0e", "_spec": "@types/react-redux@^7.1.20", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui\\node_modules\\react-redux", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/tkqubo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/clayne11"}, {"name": "<PERSON>", "url": "https://github.com/tansongyang"}, {"name": "<PERSON>", "url": "https://github.com/nicholasboll"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mdibyo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/val1984"}, {"name": "<PERSON>", "url": "https://github.com/jrakotoharisoa"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/apa<PERSON><PERSON>ski"}, {"name": "<PERSON>", "url": "https://github.com/surgeboris"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/soerenbf"}, {"name": "<PERSON>", "url": "https://github.com/mrwolfz"}, {"name": "<PERSON>", "url": "https://github.com/dylanvann"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Lazyuki"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/kazuma1989"}, {"name": "<PERSON>", "url": "https://github.com/megazazik"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/phryneas"}, {"name": "<PERSON>", "url": "https://github.com/markerikson"}], "dependencies": {"@types/hoist-non-react-statics": "^3.3.0", "@types/react": "*", "hoist-non-react-statics": "^3.3.0", "redux": "^4.0.0"}, "deprecated": false, "description": "TypeScript definitions for react-redux", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-redux", "license": "MIT", "main": "", "name": "@types/react-redux", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-redux"}, "scripts": {}, "typeScriptVersion": "4.8", "types": "index.d.ts", "typesPublisherContentHash": "0bca859870c928967a511a0732c00eb6423bfdfae1fad656ebe55b93d83599ce", "version": "7.1.34"}