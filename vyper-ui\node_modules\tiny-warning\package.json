{"_args": [["tiny-warning@1.0.3", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "tiny-warning@1.0.3", "_id": "tiny-warning@1.0.3", "_inBundle": false, "_integrity": "sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=", "_location": "/tiny-warning", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "tiny-warning@1.0.3", "name": "tiny-warning", "escapedName": "tiny-warning", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/formik", "/history", "/jss", "/jss-plugin-nested", "/jss-plugin-rule-value-function", "/mini-create-react-context", "/react-router", "/react-router-dom"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/tiny-warning/-/tiny-warning-1.0.3.tgz", "_spec": "1.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/alexreardon/tiny-warning/issues"}, "description": "A tiny warning function", "devDependencies": {"@babel/core": "^7.5.0", "@babel/preset-env": "^7.5.0", "@babel/preset-flow": "^7.0.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "^24.8.0", "flow-bin": "0.102.0", "jest": "^24.8.0", "prettier": "1.18.2", "regenerator-runtime": "^0.13.2", "rimraf": "^2.6.3", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.2"}, "files": ["/dist", "/src"], "homepage": "https://github.com/alexreardon/tiny-warning#readme", "keywords": ["warning", "warn"], "license": "MIT", "main": "dist/tiny-warning.cjs.js", "module": "dist/tiny-warning.esm.js", "name": "tiny-warning", "repository": {"type": "git", "url": "git+https://github.com/alexreardon/tiny-warning.git"}, "scripts": {"build": "yarn build:clean && yarn build:dist && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "yarn rollup --config rollup.config.js", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/tiny-warning.cjs.js.flow", "lint": "yarn prettier --debug-check src/** test/**", "prepublishOnly": "yarn build", "test": "yarn jest", "typecheck": "yarn flow", "validate": "yarn lint && yarn flow"}, "sideEffects": false, "types": "src/index.d.ts", "version": "1.0.3"}