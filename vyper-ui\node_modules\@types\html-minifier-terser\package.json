{"_args": [["@types/html-minifier-terser@5.1.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/html-minifier-terser@5.1.2", "_id": "@types/html-minifier-terser@5.1.2", "_inBundle": false, "_integrity": "sha1-aTsxatMj6pfu1rOO0aPMArFnK1c=", "_location": "/@types/html-minifier-terser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/html-minifier-terser@5.1.2", "name": "@types/html-minifier-terser", "escapedName": "@types%2fhtml-minifier-terser", "scope": "@types", "rawSpec": "5.1.2", "saveSpec": null, "fetchSpec": "5.1.2"}, "_requiredBy": ["/html-webpack-plugin"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/html-minifier-terser/-/html-minifier-terser-5.1.2.tgz", "_spec": "5.1.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "dependencies": {}, "description": "TypeScript definitions for html-minifier-terser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/html-minifier-terser", "license": "MIT", "main": "", "name": "@types/html-minifier-terser", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/html-minifier-terser"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "28cce5825a3174a32e03b0b5748729c78d53810f69cf72f48bc2faa9f7af6fb7", "version": "5.1.2"}