{"_args": [["@material-ui/styles@4.11.5", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@material-ui/styles@4.11.5", "_id": "@material-ui/styles@4.11.5", "_inBundle": false, "_integrity": "sha1-GfhEV986r9lWrIY9vhVrHYjiu/s=", "_location": "/@material-ui/styles", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@material-ui/styles@4.11.5", "name": "@material-ui/styles", "escapedName": "@material-ui%2fstyles", "scope": "@material-ui", "rawSpec": "4.11.5", "saveSpec": null, "fetchSpec": "4.11.5"}, "_requiredBy": ["/", "/@material-ui/core"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@material-ui/styles/-/styles-4.11.5.tgz", "_spec": "4.11.5", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "Material-UI Team"}, "bugs": {"url": "https://github.com/mui-org/material-ui/issues"}, "dependencies": {"@babel/runtime": "^7.4.4", "@emotion/hash": "^0.8.0", "@material-ui/types": "5.1.0", "@material-ui/utils": "^4.11.3", "clsx": "^1.0.4", "csstype": "^2.5.2", "hoist-non-react-statics": "^3.3.2", "jss": "^10.5.1", "jss-plugin-camel-case": "^10.5.1", "jss-plugin-default-unit": "^10.5.1", "jss-plugin-global": "^10.5.1", "jss-plugin-nested": "^10.5.1", "jss-plugin-props-sort": "^10.5.1", "jss-plugin-rule-value-function": "^10.5.1", "jss-plugin-vendor-prefixer": "^10.5.1", "prop-types": "^15.7.2"}, "description": "Material-UI Styles - The styling solution of Material-UI.", "engines": {"node": ">=8.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/material-ui"}, "homepage": "https://github.com/mui-org/material-ui/tree/master/packages/material-ui-styles", "keywords": ["react", "react-component", "material design", "material-ui", "styles"], "license": "MIT", "main": "./index.js", "module": "./esm/index.js", "name": "@material-ui/styles", "peerDependencies": {"@types/react": "^16.8.6 || ^17.0.0", "react": "^16.8.0 || ^17.0.0", "react-dom": "^16.8.0 || ^17.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "private": false, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/mui-org/material-ui.git", "directory": "packages/material-ui-styles"}, "sideEffects": false, "typings": "./index.d.ts", "version": "4.11.5"}