{"_args": [["moment@2.29.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "moment@2.29.1", "_id": "moment@2.29.1", "_inBundle": false, "_integrity": "sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M=", "_location": "/moment", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "moment@2.29.1", "name": "moment", "escapedName": "moment", "rawSpec": "2.29.1", "saveSpec": null, "fetchSpec": "2.29.1"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/moment/-/moment-2.29.1.tgz", "_spec": "2.29.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "Iskren Ivov Chernev", "email": "<EMAIL>", "url": "https://github.com/ichernev"}, "bugs": {"url": "https://github.com/moment/moment/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://timwoodcreates.com/"}, {"name": "<PERSON>", "url": "http://rockymeza.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://codeofmatt.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://isaaccambron.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/oire"}], "description": "Parse, validate, manipulate, and display dates", "devDependencies": {"benchmark": "latest", "coveralls": "latest", "cross-env": "^6.0.3", "es6-promise": "latest", "eslint": "~6", "grunt": "latest", "grunt-benchmark": "latest", "grunt-cli": "latest", "grunt-contrib-clean": "latest", "grunt-contrib-concat": "latest", "grunt-contrib-copy": "latest", "grunt-contrib-uglify": "latest", "grunt-contrib-watch": "latest", "grunt-env": "latest", "grunt-exec": "latest", "grunt-karma": "latest", "grunt-nuget": "latest", "grunt-string-replace": "latest", "karma": "latest", "karma-chrome-launcher": "latest", "karma-firefox-launcher": "latest", "karma-qunit": "latest", "karma-sauce-launcher": "4.1.4", "load-grunt-tasks": "latest", "lodash": ">=4.17.19", "node-qunit": "latest", "nyc": "latest", "prettier": "latest", "qunit": "^2.10.0", "rollup": "2.17.1", "typescript": "^1.8.10", "typescript3": "npm:typescript@^3.1.6", "uglify-js": "latest"}, "dojoBuild": "package.js", "ender": "./ender.js", "engines": {"node": "*"}, "homepage": "https://momentjs.com", "jsnext:main": "./dist/moment.js", "jspm": {"files": ["moment.js", "moment.d.ts", "locale"], "map": {"moment": "./moment"}, "buildConfig": {"uglify": true}}, "keywords": ["moment", "date", "time", "parse", "format", "validate", "i18n", "l10n", "ender"], "license": "MIT", "main": "./moment.js", "name": "moment", "repository": {"type": "git", "url": "git+https://github.com/moment/moment.git"}, "scripts": {"coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "eslint": "eslint <PERSON>le.js tasks src", "prettier-check": "prettier --check Gruntfile.js tasks src", "prettier-fmt": "prettier --write Gruntfile.js tasks src", "test": "grunt test", "ts3.1-typescript-test": "cross-env node_modules/typescript3/bin/tsc --project ts3.1-typing-tests", "typescript-test": "cross-env node_modules/typescript/bin/tsc --project typing-tests"}, "spm": {"main": "moment.js", "output": ["locale/*.js"]}, "typesVersions": {">=3.1": {"*": ["ts3.1-typings/*"]}}, "typings": "./moment.d.ts", "version": "2.29.1"}