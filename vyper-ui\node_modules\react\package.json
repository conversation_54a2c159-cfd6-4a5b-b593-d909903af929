{"_args": [["react@16.14.0", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "react@16.14.0", "_id": "react@16.14.0", "_inBundle": false, "_integrity": "sha1-lNd23dCqo32j7aj8W2sYpMmjEU0=", "_location": "/react", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "react@16.14.0", "name": "react", "escapedName": "react", "rawSpec": "16.14.0", "saveSpec": null, "fetchSpec": "16.14.0"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/react/-/react-16.14.0.tgz", "_spec": "16.14.0", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "browserify": {"transform": ["loose-envify"]}, "bugs": {"url": "https://github.com/facebook/react/issues"}, "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1", "prop-types": "^15.6.2"}, "deprecated": false, "description": "React is a JavaScript library for building user interfaces.", "engines": {"node": ">=0.10.0"}, "files": ["LICENSE", "README.md", "build-info.json", "index.js", "cjs/", "umd/", "jsx-runtime.js", "jsx-dev-runtime.js"], "homepage": "https://reactjs.org/", "keywords": ["react"], "license": "MIT", "main": "index.js", "name": "react", "repository": {"type": "git", "url": "git+https://github.com/facebook/react.git", "directory": "packages/react"}, "version": "16.14.0"}