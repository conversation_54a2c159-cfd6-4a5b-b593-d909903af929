{"_args": [["@material-ui/icons@4.11.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@material-ui/icons@4.11.2", "_id": "@material-ui/icons@4.11.2", "_inBundle": false, "_integrity": "sha1-s6c1MmZRnNdDtkYa6f38sbJetMU=", "_location": "/@material-ui/icons", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@material-ui/icons@4.11.2", "name": "@material-ui/icons", "escapedName": "@material-ui%2ficons", "scope": "@material-ui", "rawSpec": "4.11.2", "saveSpec": null, "fetchSpec": "4.11.2"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@material-ui/icons/-/icons-4.11.2.tgz", "_spec": "4.11.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "Material-UI Team"}, "bugs": {"url": "https://github.com/mui-org/material-ui/issues"}, "dependencies": {"@babel/runtime": "^7.4.4"}, "description": "Material Design Svg Icons converted to Material-UI React components.", "engines": {"node": ">=8.0.0"}, "homepage": "https://github.com/mui-org/material-ui/tree/master/packages/material-ui-icons", "keywords": ["react", "react-component", "material design", "material-ui", "icons"], "license": "MIT", "main": "./index.js", "module": "./esm/index.js", "name": "@material-ui/icons", "peerDependencies": {"@material-ui/core": "^4.0.0", "@types/react": "^16.8.6 || ^17.0.0", "react": "^16.8.0 || ^17.0.0", "react-dom": "^16.8.0 || ^17.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "private": false, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/mui-org/material-ui.git", "directory": "packages/material-ui-icons"}, "sideEffects": false, "typings": "./index.d.ts", "version": "4.11.2"}