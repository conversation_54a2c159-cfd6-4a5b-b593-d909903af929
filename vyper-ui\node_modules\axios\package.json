{"_args": [["axios@0.27.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "axios@0.27.2", "_id": "axios@0.27.2", "_inBundle": false, "_integrity": "sha1-IHZYzIYhYG5YbIXbS0GnUOdW2XI=", "_location": "/axios", "_phantomChildren": {"asynckit": "0.4.0", "combined-stream": "1.0.8", "mime-types": "2.1.34"}, "_requested": {"type": "version", "registry": true, "raw": "axios@0.27.2", "name": "axios", "escapedName": "axios", "rawSpec": "0.27.2", "saveSpec": null, "fetchSpec": "0.27.2"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/axios/-/axios-0.27.2.tgz", "_spec": "0.27.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "<PERSON>"}, "browser": {"./lib/adapters/http.js": "./lib/adapters/xhr.js", "./lib/defaults/env/FormData.js": "./lib/helpers/null.js"}, "bugs": {"url": "https://github.com/axios/axios/issues"}, "bundlesize": [{"path": "./dist/axios.min.js", "threshold": "5kB"}], "dependencies": {"follow-redirects": "^1.14.9", "form-data": "^4.0.0"}, "description": "Promise based HTTP client for the browser and node.js", "devDependencies": {"abortcontroller-polyfill": "^1.7.3", "coveralls": "^3.1.1", "dtslint": "^4.2.1", "es6-promise": "^4.2.8", "formidable": "^2.0.1", "grunt": "^1.4.1", "grunt-banner": "^0.6.0", "grunt-cli": "^1.4.3", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-eslint": "^24.0.0", "grunt-karma": "^4.0.2", "grunt-mocha-test": "^0.13.3", "grunt-webpack": "^5.0.0", "istanbul-instrumenter-loader": "^3.0.1", "jasmine-core": "^2.4.1", "karma": "^6.3.17", "karma-chrome-launcher": "^3.1.1", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^1.1.1", "karma-jasmine-ajax": "^0.1.13", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.8", "karma-webpack": "^4.0.2", "load-grunt-tasks": "^5.1.0", "minimist": "^1.2.6", "mocha": "^8.2.1", "sinon": "^4.5.0", "terser-webpack-plugin": "^4.2.3", "typescript": "^4.6.3", "url-search-params": "^0.10.0", "webpack": "^4.44.2", "webpack-dev-server": "^3.11.0"}, "homepage": "https://axios-http.com", "jsdelivr": "dist/axios.min.js", "keywords": ["xhr", "http", "ajax", "promise", "node"], "license": "MIT", "main": "index.js", "name": "axios", "repository": {"type": "git", "url": "git+https://github.com/axios/axios.git"}, "scripts": {"build": "NODE_ENV=production grunt build", "coveralls": "cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "examples": "node ./examples/server.js", "fix": "eslint --fix lib/**/*.js", "preversion": "grunt version && npm test", "start": "node ./sandbox/server.js", "test": "grunt test && dtslint"}, "types": "index.d.ts", "typings": "./index.d.ts", "unpkg": "dist/axios.min.js", "version": "0.27.2"}