{"_args": [["react-router@5.2.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "react-router@5.2.1", "_id": "react-router@5.2.1", "_inBundle": false, "_integrity": "sha1-TS5OnVrpQlCRhFuNvG2dJ2I5d00=", "_location": "/react-router", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "react-router@5.2.1", "name": "react-router", "escapedName": "react-router", "rawSpec": "5.2.1", "saveSpec": null, "fetchSpec": "5.2.1"}, "_requiredBy": ["/react-router-dom"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/react-router/-/react-router-5.2.1.tgz", "_spec": "5.2.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "React Training", "email": "<EMAIL>"}, "browserify": {"transform": ["loose-envify"]}, "bugs": {"url": "https://github.com/ReactTraining/react-router/issues"}, "dependencies": {"@babel/runtime": "^7.12.13", "history": "^4.9.0", "hoist-non-react-statics": "^3.1.0", "loose-envify": "^1.3.1", "mini-create-react-context": "^0.4.0", "path-to-regexp": "^1.7.0", "prop-types": "^15.6.2", "react-is": "^16.6.0", "tiny-invariant": "^1.0.2", "tiny-warning": "^1.0.0"}, "description": "Declarative routing for React", "files": ["MemoryRouter.js", "Prompt.js", "Redirect.js", "Route.js", "Router.js", "StaticRouter.js", "Switch.js", "cjs", "es", "esm", "index.js", "generatePath.js", "matchPath.js", "modules/*.js", "modules/utils/*.js", "withRouter.js", "warnAboutDeprecatedCJSRequire.js", "umd"], "homepage": "https://github.com/ReactTraining/react-router#readme", "keywords": ["react", "router", "route", "routing", "history", "link"], "license": "MIT", "main": "index.js", "module": "esm/react-router.js", "name": "react-router", "peerDependencies": {"react": ">=15"}, "repository": {"type": "git", "url": "git+https://github.com/ReactTraining/react-router.git"}, "scripts": {"build": "rollup -c", "lint": "eslint modules"}, "sideEffects": false, "version": "5.2.1"}