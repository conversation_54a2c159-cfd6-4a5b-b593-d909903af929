{"_args": [["@types/yargs@16.0.4", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/yargs@16.0.4", "_id": "@types/yargs@16.0.4", "_inBundle": false, "_integrity": "sha1-JqrZjdLCo45CEIbqmtQrnlFkKXc=", "_location": "/@types/yargs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/yargs@16.0.4", "name": "@types/yargs", "escapedName": "@types%2fyargs", "scope": "@types", "rawSpec": "16.0.4", "saveSpec": null, "fetchSpec": "16.0.4"}, "_requiredBy": ["/@jest/types", "/jest-runtime"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/yargs/-/yargs-16.0.4.tgz", "_spec": "16.0.4", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/poelstra"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mizunashi-mana"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/pushplay"}, {"name": "<PERSON><PERSON>", "url": "<PERSON><PERSON>"}, {"name": "Steffen Viken Valvåg", "url": "https://github.com/steffenvv"}, {"name": "<PERSON>", "url": "https://github.com/forivall"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Aankhen"}], "dependencies": {"@types/yargs-parser": "*"}, "description": "TypeScript definitions for yargs", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "license": "MIT", "main": "", "name": "@types/yargs", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/yargs"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "d46fdfa13e8760d2fb36fecd2e85de1f955504f612121a84f42188b00876dc45", "version": "16.0.4"}