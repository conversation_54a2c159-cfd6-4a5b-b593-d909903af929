import React from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import DragIndicatorIcon from "@material-ui/icons/DragIndicator";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import { Operation } from "./Operation";
import { AddRequiredButton } from "./buttons/AddRequiredButton";
import { Traveler } from './Traveler';

const useStyles = makeStyles({
  dragHandle: {
    display: "inline-flex",
    alignItems: "center",
    cursor: "grab",
    color: "#666",
    marginRight: "8px",
    "&:hover": {
      color: "#333",
    },
    "&:active": {
      cursor: "grabbing",
    },
  },
  operationWithDrag: {
    display: "flex",
    alignItems: "flex-start",
  },
  operationContent: {
    flex: 1,
  },
  draggingItem: {
    backgroundColor: "#f0f8ff",
    borderRadius: "4px",
    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
    transition: "background-color 0.2s ease, box-shadow 0.2s ease",
  }
});

export const Body = ({
  vyper,
  build,
  options,
  onEditOperation,
  onAddOperation,
  onRemoveOperation,
  onEditComponentName,
  onEditComponentValue,
  onAddComponent,
  onRemoveComponent,
  onClickValidate,
  onCommentOperation,
  approvedGroups,
  onUndoDelete,
  onSelectDiagramApproval,
  unApprovedGroups,
  dragDropChanges,
  onOperationReorder,
  onComponentReorder,
}) => {
  const determineValidatedOperation = (operation) => {
    return build.validatedOperations.find(
      (vo) => vo.operation === operation.name
    );
  };

  const isDragDropEnabled = options.dragdrop;
  const classes = useStyles();

  // If drag and drop is disabled, render the sidebar layout
  if (!isDragDropEnabled) {
    const operations = build.traveler.operations.filter(
      (operation) => operation.name !== "TEST"
    );

    const isDevicePkgNiche = build?.templateSource?.templateType === 'DEVICE_PKGNICHE' || build?.templateSource?.templateType == undefined;

    const handleSectionClick = (sectionName) => {
      const sectionElement = document.getElementById(sectionName);
      if (sectionElement) {
        const rect = sectionElement.getBoundingClientRect();
        const offset = rect.top + window.scrollY - document.querySelector('.optionBar').offsetHeight - 50;
        window.scrollTo({ top: offset, behavior: "smooth" });
      }
    };

    return (
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div style={{
          width: '250px',
          height: '50vh',
          position: 'sticky',
          fontSize: '0.8rem',
          top: isDevicePkgNiche ? '300px' : '375px',
          left: '0',
          zIndex: 1,
          overflowY: 'auto',
          border: '1px solid #ccc',
          borderBottom: '1px solid #ccc',
          padding: '20px',
          paddingBottom: '10%',
          boxSizing: 'border-box',
          flexShrink: 0,
        }}>
          <h2>Operations</h2>
          <ul>
            {operations.map((operation, index) => (
              <li key={index}>
                <a style={{ color: "blue" }}
                  href={`#operation-${index}`}
                  onClick={() => handleSectionClick(`operation-${index}`)}
                >
                  {operation.name}
                </a>
              </li>
            ))}
          </ul>
        </div>
        <div style={{ paddingLeft: '40px', flex: 1, minWidth: '0', overflowX: 'auto' }}>
          <Traveler vyper={vyper} build={build} options={options} />
          {options.editbutton ? (
            <AddRequiredButton
              title="Add Operation"
              onClick={() => onAddOperation(null)}
            />
          ) : null}

          {operations.map((operation, index) => (
            <div id={`operation-${index}`} key={index} style={{ scrollMarginTop: isDevicePkgNiche ? '256px' : '327px' }}>
              <Operation
                vyper={vyper}
                build={build}
                operation={operation}
                operationIndex={index}
                options={options}
                onEditOperation={(o) => onEditOperation(o, index)}
                onAddOperation={(o) => onAddOperation(o, index)}
                onRemoveOperation={(o) => onRemoveOperation(o, index)}
                onEditComponentName={(o, c, cPos) =>
                  onEditComponentName(o, c, index, cPos)
                }
                onEditComponentValue={(o, c, cPos) =>
                  onEditComponentValue(o, c, index, cPos)
                }
                onAddComponent={(o, c, cPos) => onAddComponent(o, c, index, cPos)}
                onRemoveComponent={(o, c, cPos) => onRemoveComponent(o, c, index, cPos)}
                validatedOperation={determineValidatedOperation(operation)}
                onClickValidate={onClickValidate}
                onCommentOperation={onCommentOperation}
                approvedGroups={approvedGroups}
                onUndoDelete={(o) => onUndoDelete(o)}
                reworkedTraveler={build?.reworkedTraveler}
                onSelectDiagramApproval={onSelectDiagramApproval}
                unApprovedGroups={unApprovedGroups}
              />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Drag and drop enabled mode
  // Use drag drop changes if available, otherwise use build operations
  const operationsToRender = dragDropChanges?.operations || build.traveler.operations;

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    // Check if it's an operation reorder (droppableId starts with "operations")
    if (result.source.droppableId === "operations" && result.destination.droppableId === "operations") {
      onOperationReorder(result);
    } else {
      // It's a component reorder
      onComponentReorder(result);
    }
  };

  const renderOperations = () => {
    return operationsToRender.map((operation, n) =>
      operation.name === "TEST" ? null : (
        <Draggable
          key={operation.name}
          draggableId={`operation-${operation.name}`}
          index={n}
          isDragDisabled={!isDragDropEnabled}
        >
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.draggableProps}
              style={provided.draggableProps.style}
              className={clsx(
                classes.operationWithDrag,
                snapshot.isDragging && classes.draggingItem
              )}
            >
              <div className={classes.operationWithDrag}>
                {isDragDropEnabled && (
                  <div {...provided.dragHandleProps} className={classes.dragHandle}>
                    <DragIndicatorIcon fontSize="small" />
                  </div>
                )}
                <div className={classes.operationContent}>
                  <Operation
                    key={n}
                    vyper={vyper}
                    build={build}
                    operation={operation}
                    operationIndex={n}
                    options={options}
                    onEditOperation={(o) => onEditOperation(o, n)}
                    onAddOperation={(o) => onAddOperation(o, n)}
                    onRemoveOperation={(o) => onRemoveOperation(o, n)}
                    onEditComponentName={(o, c, cPos) =>
                      onEditComponentName(o, c, n, cPos)
                    }
                    onEditComponentValue={(o, c, cPos) =>
                      onEditComponentValue(o, c, n, cPos)
                    }
                    onAddComponent={(o, c, cPos) => onAddComponent(o, c, n, cPos)}
                    onRemoveComponent={(o, c, cPos) => onRemoveComponent(o, c, n, cPos)}
                    validatedOperation={determineValidatedOperation(operation)}
                    onClickValidate={onClickValidate}
                    onCommentOperation={onCommentOperation}
                    approvedGroups={approvedGroups}
                    onUndoDelete={(o) => onUndoDelete(o)}
                    reworkedTraveler={build?.reworkedTraveler}
                    onSelectDiagramApproval={onSelectDiagramApproval}
                    unApprovedGroups={unApprovedGroups}
                    isDragDropEnabled={isDragDropEnabled}
                    onComponentReorder={onComponentReorder}
                  />
                </div>
              </div>
            </div>
          )}
        </Draggable>
      )
    );
  };

  return (
    <div>
      {options.editbutton ? (
        <AddRequiredButton
          title="Add Operation"
          onClick={() => onAddOperation(null)}
        />
      ) : null}

      {isDragDropEnabled ? (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="operations" type="operation">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                {renderOperations()}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      ) : (
        <div>
          {operationsToRender.map((operation, n) =>
            operation.name === "TEST" ? null : (
              <div key={operation.name}>
                <Operation
                  key={n}
                  vyper={vyper}
                  build={build}
                  operation={operation}
                  operationIndex={n}
                  options={options}
                  onEditOperation={(o) => onEditOperation(o, n)}
                  onAddOperation={(o) => onAddOperation(o, n)}
                  onRemoveOperation={(o) => onRemoveOperation(o, n)}
                  onEditComponentName={(o, c, cPos) =>
                    onEditComponentName(o, c, n, cPos)
                  }
                  onEditComponentValue={(o, c, cPos) =>
                    onEditComponentValue(o, c, n, cPos)
                  }
                  onAddComponent={(o, c, cPos) => onAddComponent(o, c, n, cPos)}
                  onRemoveComponent={(o, c, cPos) => onRemoveComponent(o, c, n, cPos)}
                  validatedOperation={determineValidatedOperation(operation)}
                  onClickValidate={onClickValidate}
                  onCommentOperation={onCommentOperation}
                  approvedGroups={approvedGroups}
                  onUndoDelete={(o) => onUndoDelete(o)}
                  reworkedTraveler={build?.reworkedTraveler}
                  onSelectDiagramApproval={onSelectDiagramApproval}
                  unApprovedGroups={unApprovedGroups}
                  isDragDropEnabled={isDragDropEnabled}
                  onComponentReorder={onComponentReorder}
                />
              </div>
            )
          )}
        </div>
      )}
    </div>
  );
};