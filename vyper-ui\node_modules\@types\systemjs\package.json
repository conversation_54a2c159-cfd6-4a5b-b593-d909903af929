{"_args": [["@types/systemjs@6.1.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/systemjs@6.1.1", "_id": "@types/systemjs@6.1.1", "_inBundle": false, "_integrity": "sha1-6uF/KggOhn0Bot1hT1JKsifPWkE=", "_location": "/@types/systemjs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/systemjs@6.1.1", "name": "@types/systemjs", "escapedName": "@types%2fsystemjs", "scope": "@types", "rawSpec": "6.1.1", "saveSpec": null, "fetchSpec": "6.1.1"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/systemjs/-/systemjs-6.1.1.tgz", "_spec": "6.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/joeldenning"}], "dependencies": {}, "description": "TypeScript definitions for SystemJS", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/systemjs", "license": "MIT", "main": "", "name": "@types/systemjs", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/systemjs"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "6b27c12ec37dbb90467395c847da3cc7b59d26df3c06a1c14d6338bacbdd3e1f", "version": "6.1.1"}