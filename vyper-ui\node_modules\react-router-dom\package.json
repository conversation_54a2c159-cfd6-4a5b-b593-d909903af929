{"_args": [["react-router-dom@5.3.0", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "react-router-dom@5.3.0", "_id": "react-router-dom@5.3.0", "_inBundle": false, "_integrity": "sha1-2hv7U1oOiacSqTuX3Xb0etHzI2M=", "_location": "/react-router-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "react-router-dom@5.3.0", "name": "react-router-dom", "escapedName": "react-router-dom", "rawSpec": "5.3.0", "saveSpec": null, "fetchSpec": "5.3.0"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/react-router-dom/-/react-router-dom-5.3.0.tgz", "_spec": "5.3.0", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "React Training", "email": "<EMAIL>"}, "browserify": {"transform": ["loose-envify"]}, "bugs": {"url": "https://github.com/ReactTraining/react-router/issues"}, "dependencies": {"@babel/runtime": "^7.12.13", "history": "^4.9.0", "loose-envify": "^1.3.1", "prop-types": "^15.6.2", "react-router": "5.2.1", "tiny-invariant": "^1.0.2", "tiny-warning": "^1.0.0"}, "description": "DOM bindings for React Router", "files": ["BrowserRouter.js", "HashRouter.js", "Link.js", "MemoryRouter.js", "NavLink.js", "Prompt.js", "Redirect.js", "Route.js", "Router.js", "StaticRouter.js", "Switch.js", "cjs", "es", "esm", "index.js", "generatePath.js", "matchPath.js", "modules/*.js", "modules/utils/*.js", "withRouter.js", "warnAboutDeprecatedCJSRequire.js", "umd"], "homepage": "https://github.com/ReactTraining/react-router#readme", "keywords": ["react", "router", "route", "routing", "history", "link"], "license": "MIT", "main": "index.js", "module": "esm/react-router-dom.js", "name": "react-router-dom", "peerDependencies": {"react": ">=15"}, "repository": {"type": "git", "url": "git+https://github.com/ReactTraining/react-router.git"}, "scripts": {"build": "rollup -c", "lint": "eslint modules"}, "sideEffects": false, "version": "5.3.0"}