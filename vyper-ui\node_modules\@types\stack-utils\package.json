{"_args": [["@types/stack-utils@2.0.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/stack-utils@2.0.1", "_id": "@types/stack-utils@2.0.1", "_inBundle": false, "_integrity": "sha1-IPGClPeX8iCbX2XI47XI6CYdEnw=", "_location": "/@types/stack-utils", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/stack-utils@2.0.1", "name": "@types/stack-utils", "escapedName": "@types%2fstack-utils", "scope": "@types", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/jest-message-util"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/stack-utils/-/stack-utils-2.0.1.tgz", "_spec": "2.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "dependencies": {}, "description": "TypeScript definitions for stack-utils", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-utils", "license": "MIT", "main": "", "name": "@types/stack-utils", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/stack-utils"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "bfc4261f4e970131a82704cd51ce862a2f5c7e33c5447030510300c6fe1ee268", "version": "2.0.1"}