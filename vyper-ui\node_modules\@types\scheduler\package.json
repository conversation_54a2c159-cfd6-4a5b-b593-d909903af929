{"_args": [["@types/scheduler@0.16.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@types/scheduler@0.16.2", "_id": "@types/scheduler@0.16.2", "_inBundle": false, "_integrity": "sha1-GmL4lSVyPd4kuhsBsJK/XfitTTk=", "_location": "/@types/scheduler", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/scheduler@0.16.2", "name": "@types/scheduler", "escapedName": "@types%2fscheduler", "scope": "@types", "rawSpec": "0.16.2", "saveSpec": null, "fetchSpec": "0.16.2"}, "_requiredBy": ["/@types/react"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/scheduler/-/scheduler-0.16.2.tgz", "_spec": "0.16.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/Methuselah96"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}], "dependencies": {}, "description": "TypeScript definitions for scheduler", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "license": "MIT", "main": "", "name": "@types/scheduler", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/scheduler"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "122d740959245799b89613cc799b1a2e3274d1ee1db6c9abd7b6e4dadc0696ec", "version": "0.16.2"}