{"_args": [["@types/istanbul-lib-report@3.0.0", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/istanbul-lib-report@3.0.0", "_id": "@types/istanbul-lib-report@3.0.0", "_inBundle": false, "_integrity": "sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=", "_location": "/@types/istanbul-lib-report", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/istanbul-lib-report@3.0.0", "name": "@types/istanbul-lib-report", "escapedName": "@types%2fistanbul-lib-report", "scope": "@types", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/@types/istanbul-reports"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "_spec": "3.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/jason0x43"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/zache"}], "dependencies": {"@types/istanbul-lib-coverage": "*"}, "description": "TypeScript definitions for istanbul-lib-report", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/istanbul-lib-report", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/istanbul-lib-report"}, "scripts": {}, "typeScriptVersion": "2.8", "types": "index.d.ts", "typesPublisherContentHash": "f8b2f5e15a24d9f52a96c5cfadb0f582bf6200ce8643e15422c3c8f1a2bb1c63", "version": "3.0.0"}