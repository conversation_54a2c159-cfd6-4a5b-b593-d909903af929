{"_args": [["prop-types@15.8.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "prop-types@15.8.1", "_id": "prop-types@15.8.1", "_inBundle": false, "_integrity": "sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=", "_location": "/prop-types", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "prop-types@15.8.1", "name": "prop-types", "escapedName": "prop-types", "rawSpec": "15.8.1", "saveSpec": null, "fetchSpec": "15.8.1"}, "_requiredBy": ["/", "/@material-ui/core", "/@material-ui/lab", "/@material-ui/styles", "/@material-ui/system", "/@material-ui/utils", "/ag-grid-react", "/eslint-plugin-react", "/react", "/react-dom", "/react-popper", "/react-redux", "/react-router", "/react-router-dom", "/react-transition-group", "/reactstrap", "/reactstrap/react-transition-group"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/prop-types/-/prop-types-15.8.1.tgz", "_spec": "15.8.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "browserify": {"transform": ["loose-envify"]}, "bugs": {"url": "https://github.com/facebook/prop-types/issues"}, "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}, "description": "Runtime type checking for React props and similar objects.", "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^16.5.0", "bundle-collapser": "^1.4.0", "eslint": "^8.6.0", "in-publish": "^2.0.1", "jest": "^19.0.2", "react": "^15.7.0", "uglifyify": "^5.0.2", "uglifyjs": "^2.4.11"}, "files": ["LICENSE", "README.md", "checkPropTypes.js", "factory.js", "factoryWithThrowingShims.js", "factoryWithTypeCheckers.js", "index.js", "prop-types.js", "prop-types.min.js", "lib"], "homepage": "https://facebook.github.io/react/", "keywords": ["react"], "license": "MIT", "main": "index.js", "name": "prop-types", "repository": {"type": "git", "url": "git+https://github.com/facebook/prop-types.git"}, "scripts": {"build": "yarn umd && yarn umd-min", "lint": "eslint .", "prepublish": "not-in-publish || yarn build", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "jest", "umd": "NODE_ENV=development browserify index.js -t loose-envify --standalone PropTypes -o prop-types.js", "umd-min": "NODE_ENV=production browserify index.js -t loose-envify -t uglifyify --standalone PropTypes  -p bundle-collapser/plugin -o | uglifyjs --compress unused,dead_code -o prop-types.min.js"}, "sideEffects": false, "version": "15.8.1"}