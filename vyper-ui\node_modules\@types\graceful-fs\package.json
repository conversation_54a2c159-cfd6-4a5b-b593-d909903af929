{"_args": [["@types/graceful-fs@4.1.5", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/graceful-fs@4.1.5", "_id": "@types/graceful-fs@4.1.5", "_inBundle": false, "_integrity": "sha1-If+6DZjaQ1DbZIkfkqnl2zzbThU=", "_location": "/@types/graceful-fs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/graceful-fs@4.1.5", "name": "@types/graceful-fs", "escapedName": "@types%2fgraceful-fs", "scope": "@types", "rawSpec": "4.1.5", "saveSpec": null, "fetchSpec": "4.1.5"}, "_requiredBy": ["/jest-haste-map"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/graceful-fs/-/graceful-fs-4.1.5.tgz", "_spec": "4.1.5", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/Bartvds"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "dependencies": {"@types/node": "*"}, "description": "TypeScript definitions for graceful-fs", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/graceful-fs", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/graceful-fs"}, "scripts": {}, "typeScriptVersion": "3.9", "types": "index.d.ts", "typesPublisherContentHash": "3e053765471f17a5b5cdd161ea0773a8d7dc77e032922e2c4cae1393e2c66ac4", "version": "4.1.5"}