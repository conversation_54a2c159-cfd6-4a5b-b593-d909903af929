{"_args": [["@types/testing-library__jest-dom@5.14.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/testing-library__jest-dom@5.14.2", "_id": "@types/testing-library__jest-dom@5.14.2", "_inBundle": false, "_integrity": "sha1-Vk+ystyCcUfpN6dbY5oF0Xzhi0Q=", "_location": "/@types/testing-library__jest-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/testing-library__jest-dom@5.14.2", "name": "@types/testing-library__jest-dom", "escapedName": "@types%2ftesting-library__jest-dom", "scope": "@types", "rawSpec": "5.14.2", "saveSpec": null, "fetchSpec": "5.14.2"}, "_requiredBy": ["/@testing-library/jest-dom"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/testing-library__jest-dom/-/testing-library__jest-dom-5.14.2.tgz", "_spec": "5.14.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/gnapse"}, {"name": "<PERSON>", "url": "https://github.com/jgoz"}, {"name": "<PERSON>", "url": "https://github.com/smacpherson64"}], "dependencies": {"@types/jest": "*"}, "description": "TypeScript definitions for @testing-library/jest-dom", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/testing-library__jest-dom", "license": "MIT", "main": "", "name": "@types/testing-library__jest-dom", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/testing-library__jest-dom"}, "scripts": {}, "typeScriptVersion": "3.8", "types": "index.d.ts", "typesPublisherContentHash": "10b70f1867fa99008deb83a73eea3e3f8821f74970c647ad2be2d5654dec76d7", "version": "5.14.2"}