{"_args": [["@types/tapable@1.0.8", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/tapable@1.0.8", "_id": "@types/tapable@1.0.8", "_inBundle": false, "_integrity": "sha1-uUpDkchWZse3Mpn9OtedT6pDUxA=", "_location": "/@types/tapable", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/tapable@1.0.8", "name": "@types/tapable", "escapedName": "@types%2ftapable", "scope": "@types", "rawSpec": "1.0.8", "saveSpec": null, "fetchSpec": "1.0.8"}, "_requiredBy": ["/@types/webpack", "/html-webpack-plugin"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/tapable/-/tapable-1.0.8.tgz", "_spec": "1.0.8", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}], "dependencies": {}, "description": "TypeScript definitions for tapable", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tapable", "license": "MIT", "main": "", "name": "@types/tapable", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/tapable"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "14ebc399c2b0cbceb7a2266ba4eff57b8eaeb39dafafa3b208ec3a10fd3ccc47", "version": "1.0.8"}