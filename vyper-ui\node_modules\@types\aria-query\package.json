{"_args": [["@types/aria-query@4.2.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/aria-query@4.2.2", "_id": "@types/aria-query@4.2.2", "_inBundle": false, "_integrity": "sha1-7U4K2SMGpwT5+xMqDPz3dIbb4rw=", "_location": "/@types/aria-query", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/aria-query@4.2.2", "name": "@types/aria-query", "escapedName": "@types%2faria-query", "scope": "@types", "rawSpec": "4.2.2", "saveSpec": null, "fetchSpec": "4.2.2"}, "_requiredBy": ["/@testing-library/dom"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/aria-query/-/aria-query-4.2.2.tgz", "_spec": "4.2.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/eps1lon"}], "dependencies": {}, "description": "TypeScript definitions for aria-query", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/aria-query", "license": "MIT", "main": "", "name": "@types/aria-query", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/aria-query"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "4094e62823ffb018c2ec51d16482a29e2ccca6c2a001af679e6621715f58c651", "version": "4.2.2"}