{"_args": [["@material-ui/core@4.12.3", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@material-ui/core@4.12.3", "_id": "@material-ui/core@4.12.3", "_inBundle": false, "_integrity": "sha1-gNZlyvDx8DTlI1XFRQwOOLCZ08o=", "_location": "/@material-ui/core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@material-ui/core@4.12.3", "name": "@material-ui/core", "escapedName": "@material-ui%2fcore", "scope": "@material-ui", "rawSpec": "4.12.3", "saveSpec": null, "fetchSpec": "4.12.3"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@material-ui/core/-/core-4.12.3.tgz", "_spec": "4.12.3", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "Material-UI Team"}, "bugs": {"url": "https://github.com/mui-org/material-ui/issues"}, "dependencies": {"@babel/runtime": "^7.4.4", "@material-ui/styles": "^4.11.4", "@material-ui/system": "^4.12.1", "@material-ui/types": "5.1.0", "@material-ui/utils": "^4.11.2", "@types/react-transition-group": "^4.2.0", "clsx": "^1.0.4", "hoist-non-react-statics": "^3.3.2", "popper.js": "1.16.1-lts", "prop-types": "^15.7.2", "react-is": "^16.8.0 || ^17.0.0", "react-transition-group": "^4.4.0"}, "description": "React components that implement Google's Material Design.", "engines": {"node": ">=8.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/material-ui"}, "homepage": "https://material-ui.com/", "keywords": ["react", "react-component", "material design", "material-ui"], "license": "MIT", "main": "./index.js", "module": "./esm/index.js", "name": "@material-ui/core", "peerDependencies": {"@types/react": "^16.8.6 || ^17.0.0", "react": "^16.8.0 || ^17.0.0", "react-dom": "^16.8.0 || ^17.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "private": false, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/mui-org/material-ui.git", "directory": "packages/material-ui"}, "sideEffects": false, "typings": "./index.d.ts", "version": "4.12.3"}