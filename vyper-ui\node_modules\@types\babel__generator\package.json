{"_args": [["@types/babel__generator@7.6.3", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/babel__generator@7.6.3", "_id": "@types/babel__generator@7.6.3", "_inBundle": false, "_integrity": "sha1-9Fa0ss55E392iqEw0kI9LwzPq6U=", "_location": "/@types/babel__generator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/babel__generator@7.6.3", "name": "@types/babel__generator", "escapedName": "@types%2fbabel__generator", "scope": "@types", "rawSpec": "7.6.3", "saveSpec": null, "fetchSpec": "7.6.3"}, "_requiredBy": ["/@types/babel__core"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/babel__generator/-/babel__generator-7.6.3.tgz", "_spec": "7.6.3", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "url": "https://github.com/johnnyest<PERSON>s"}, {"name": "<PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "<PERSON>", "url": "https://github.com/khell"}, {"name": "Lyanbin", "url": "https://github.com/Lyanbin"}], "dependencies": {"@babel/types": "^7.0.0"}, "description": "TypeScript definitions for @babel/generator", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__generator", "license": "MIT", "main": "", "name": "@types/babel__generator", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__generator"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "5d63022fd7a5ff8a46344ce18d64ce05c4bf5fa887a0e3f10ab40314531a5369", "version": "7.6.3"}