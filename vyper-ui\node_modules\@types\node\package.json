{"_args": [["@types/node@16.11.12", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/node@16.11.12", "_id": "@types/node@16.11.12", "_inBundle": false, "_integrity": "sha1-rH+2k6xYfuGCw3gMJutlVGoaPBA=", "_location": "/@types/node", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/node@16.11.12", "name": "@types/node", "escapedName": "@types%2fnode", "scope": "@types", "rawSpec": "16.11.12", "saveSpec": null, "fetchSpec": "16.11.12"}, "_requiredBy": ["/@jest/console", "/@jest/core", "/@jest/environment", "/@jest/fake-timers", "/@jest/reporters", "/@jest/types", "/@types/glob", "/@types/graceful-fs", "/@types/webpack", "/@types/webpack-sources", "/jest-circus", "/jest-environment-jsdom", "/jest-environment-node", "/jest-haste-map", "/jest-jasmine2", "/jest-mock", "/jest-runner", "/jest-serializer", "/jest-util", "/jest-watcher", "/jest-worker"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/node/-/node-16.11.12.tgz", "_spec": "16.11.12", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis"}, {"name": "<PERSON>", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "url": "https://github.com/btoueg"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89"}, {"name": "<PERSON>", "url": "https://github.com/touffy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas"}, {"name": "<PERSON>", "url": "https://github.com/eyqs"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin"}, {"name": "<PERSON>", "url": "https://github.com/ajafff"}, {"name": "Lishude", "url": "https://github.com/islishude"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1"}, {"name": "<PERSON>", "url": "https://github.com/n-e"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/westy92"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick"}, {"name": "<PERSON>", "url": "https://github.com/ThomasdenH"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3"}, {"name": "<PERSON>", "url": "https://github.com/samuela"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bhongy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chyzwar"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/trivikr"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/yoursunny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/qwelias"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>-<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON>", "url": "https://github.com/victorperin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZYSzys"}, {"name": "NodeJS Contributors", "url": "https://github.com/NodeJS"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU"}, {"name": "wafuwafu13", "url": "https://github.com/wafuwafu13"}], "dependencies": {}, "description": "TypeScript definitions for Node.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node", "license": "MIT", "main": "", "name": "@types/node", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "typeScriptVersion": "3.8", "types": "index.d.ts", "typesPublisherContentHash": "66478bcf856b451a83d797fa3c4495aeb7e18e217db90d6545b2711843bfe71c", "version": "16.11.12"}