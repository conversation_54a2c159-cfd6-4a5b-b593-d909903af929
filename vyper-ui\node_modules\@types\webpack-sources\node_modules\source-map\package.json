{"_args": [["source-map@0.7.3", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "source-map@0.7.3", "_id": "source-map@0.7.3", "_inBundle": false, "_integrity": "sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=", "_location": "/@types/webpack-sources/source-map", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "source-map@0.7.3", "name": "source-map", "escapedName": "source-map", "rawSpec": "0.7.3", "saveSpec": null, "fetchSpec": "0.7.3"}, "_requiredBy": ["/@types/webpack-sources"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/source-map/-/source-map-0.7.3.tgz", "_spec": "0.7.3", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generates and consumes source maps", "devDependencies": {"doctoc": "^0.15.0", "eslint": "^4.19.1", "live-server": "^1.2.0", "npm-run-all": "^4.1.2", "nyc": "^11.7.1", "watch": "^1.0.2", "webpack": "^3.10"}, "engines": {"node": ">= 8"}, "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "homepage": "https://github.com/mozilla/source-map", "license": "BSD-3-<PERSON><PERSON>", "main": "./source-map.js", "name": "source-map", "nyc": {"reporter": "html"}, "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "scripts": {"build": "webpack --color", "clean": "rm -rf coverage .nyc_output", "coverage": "nyc node test/run-tests.js", "dev": "npm-run-all -p --silent dev:*", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "dev:watch": "watch 'npm run coverage' lib/ test/", "lint": "eslint *.js lib/ test/", "prebuild": "npm run lint", "precoverage": "npm run build", "predev": "npm run setup", "pretest": "npm run build", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "test": "node test/run-tests.js", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "types": "./source-map.d.ts", "typings": "source-map", "version": "0.7.3"}