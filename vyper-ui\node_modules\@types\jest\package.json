{"_args": [["@types/jest@27.0.3", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/jest@27.0.3", "_id": "@types/jest@27.0.3", "_inBundle": false, "_integrity": "sha1-DPnf6QCeRn9wo0Lw+U6tGYQqeDo=", "_location": "/@types/jest", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/jest@27.0.3", "name": "@types/jest", "escapedName": "@types%2fjest", "scope": "@types", "rawSpec": "27.0.3", "saveSpec": null, "fetchSpec": "27.0.3"}, "_requiredBy": ["#DEV:/", "/@types/testing-library__jest-dom"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/jest/-/jest-27.0.3.tgz", "_spec": "27.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "jwbay", "url": "https://github.com/jwbay"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/asvet<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/alexjoverm"}, {"name": "<PERSON>", "url": "https://github.com/epicallan"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ikatyang"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/wsmd"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/douglasduteil"}, {"name": "Ahn", "url": "https://github.com/ahnpnl"}, {"name": "<PERSON>", "url": "https://github.com/UselessPickles"}, {"name": "<PERSON>", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "url": "https://github.com/hotell"}, {"name": "<PERSON>", "url": "https://github.com/sebald"}, {"name": "<PERSON>", "url": "https://github.com/andys8"}, {"name": "<PERSON>", "url": "https://github.com/antoinebrault"}, {"name": "<PERSON>", "url": "https://github.com/gstamac"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON>", "url": "https://github.com/quassnoi"}, {"name": "<PERSON>", "url": "https://github.com/Belco90"}, {"name": "<PERSON>", "url": "https://github.com/tonyhallett"}, {"name": "<PERSON>", "url": "https://github.com/ycmjason"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/devanshj"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/regevbr"}, {"name": "<PERSON>", "url": "https://github.com/gerkindev"}, {"name": "<PERSON>", "url": "https://github.com/domdomegg"}], "dependencies": {"jest-diff": "^27.0.0", "pretty-format": "^27.0.0"}, "description": "TypeScript definitions for Jest", "exports": {".": {"types": "./index.d.ts"}}, "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jest", "license": "MIT", "main": "", "name": "@types/jest", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jest"}, "scripts": {}, "typeScriptVersion": "3.8", "types": "index.d.ts", "typesPublisherContentHash": "6f752af1f5656871838f597b99eef05e65227d46d44f5fdb04376aac96234ff4", "version": "27.0.3"}