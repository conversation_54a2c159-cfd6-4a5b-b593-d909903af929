{"_args": [["@types/prop-types@15.7.4", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@types/prop-types@15.7.4", "_id": "@types/prop-types@15.7.4", "_inBundle": false, "_integrity": "sha1-/PcgXCXf95Xuea8eMNosl5CAjxE=", "_location": "/@types/prop-types", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/prop-types@15.7.4", "name": "@types/prop-types", "escapedName": "@types%2fprop-types", "scope": "@types", "rawSpec": "15.7.4", "saveSpec": null, "fetchSpec": "15.7.4"}, "_requiredBy": ["/@types/react"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/prop-types/-/prop-types-15.7.4.tgz", "_spec": "15.7.4", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ferdaber"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}], "dependencies": {}, "description": "TypeScript definitions for prop-types", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/prop-types", "license": "MIT", "main": "", "name": "@types/prop-types", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/prop-types"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "261f968d22ac28d057fe4ab49115e62efdd66bb6d56ed3ff4bf96549fa3651d9", "version": "15.7.4"}