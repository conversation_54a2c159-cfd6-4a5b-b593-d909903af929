{"_args": [["@types/react-transition-group@4.4.4", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@types/react-transition-group@4.4.4", "_id": "@types/react-transition-group@4.4.4", "_inBundle": false, "_integrity": "sha1-rNTM6qK+a3V9th7XtDLhAyQtFj4=", "_location": "/@types/react-transition-group", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/react-transition-group@4.4.4", "name": "@types/react-transition-group", "escapedName": "@types%2freact-transition-group", "scope": "@types", "rawSpec": "4.4.4", "saveSpec": null, "fetchSpec": "4.4.4"}, "_requiredBy": ["/@material-ui/core"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/react-transition-group/-/react-transition-group-4.4.4.tgz", "_spec": "4.4.4", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "Epskampie", "url": "https://github.com/Epskampie"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ybiquitous"}, {"name": "tu4mo", "url": "https://github.com/tu4mo"}, {"name": "<PERSON>", "url": "https://github.com/bengry"}], "dependencies": {"@types/react": "*"}, "description": "TypeScript definitions for react-transition-group", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-transition-group", "license": "MIT", "main": "", "name": "@types/react-transition-group", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-transition-group"}, "scripts": {}, "typeScriptVersion": "3.7", "types": "index.d.ts", "typesPublisherContentHash": "c8e9884645c31c9e4894dfc6c5f84cc99c43aa5200af3019b9ec40ed51687fb0", "version": "4.4.4"}