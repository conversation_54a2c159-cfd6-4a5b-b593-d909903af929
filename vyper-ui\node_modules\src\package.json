{"_args": [["src@1.1.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "src@1.1.2", "_id": "src@1.1.2", "_inBundle": false, "_integrity": "sha1-eKvdHAjKyibMbPRb1YC1bZOs+38=", "_location": "/src", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "src@1.1.2", "name": "src", "escapedName": "src", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/src/-/src-1.1.2.tgz", "_spec": "1.1.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/wlaurance/src/issues"}, "dependencies": {"redis-url": "~0.2.0", "underscore": "~1.6.0", "uuid": "~1.4.1"}, "description": "Simple Redis Cache", "devDependencies": {"async": "~0.2.10", "mocha": "~1.17.1", "underscore": "~1.6.0"}, "homepage": "https://github.com/wlaurance/src#readme", "keywords": ["Simple", "Redis", "<PERSON><PERSON>"], "license": "MIT", "main": "index.js", "name": "src", "repository": {"type": "git", "url": "git://github.com/wlaurance/src.git"}, "scripts": {"test": "mocha -t 4000"}, "version": "1.1.2"}