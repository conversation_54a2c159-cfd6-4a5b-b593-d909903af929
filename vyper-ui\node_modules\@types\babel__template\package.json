{"_args": [["@types/babel__template@7.4.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/babel__template@7.4.1", "_id": "@types/babel__template@7.4.1", "_inBundle": false, "_integrity": "sha1-PRpI/Z1sDt/Vby/1eNrtSPNsiWk=", "_location": "/@types/babel__template", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/babel__template@7.4.1", "name": "@types/babel__template", "escapedName": "@types%2fbabel__template", "scope": "@types", "rawSpec": "7.4.1", "saveSpec": null, "fetchSpec": "7.4.1"}, "_requiredBy": ["/@types/babel__core"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/babel__template/-/babel__template-7.4.1.tgz", "_spec": "7.4.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "url": "https://github.com/marvinhagemeister"}, {"name": "<PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}], "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}, "description": "TypeScript definitions for @babel/template", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template", "license": "MIT", "main": "", "name": "@types/babel__template", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__template"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "8b488d3b882af8da43e3bb35fc9efcf3bd5fdaec465b98ed620d47b2231b62ed", "version": "7.4.1"}