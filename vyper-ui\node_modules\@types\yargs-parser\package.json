{"_args": [["@types/yargs-parser@20.2.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/yargs-parser@20.2.1", "_id": "@types/yargs-parser@20.2.1", "_inBundle": false, "_integrity": "sha1-O5ziSJkZ2eT+pDm3aRarw0st8Sk=", "_location": "/@types/yargs-parser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/yargs-parser@20.2.1", "name": "@types/yargs-parser", "escapedName": "@types%2fyargs-parser", "scope": "@types", "rawSpec": "20.2.1", "saveSpec": null, "fetchSpec": "20.2.1"}, "_requiredBy": ["/@types/yargs"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/yargs-parser/-/yargs-parser-20.2.1.tgz", "_spec": "20.2.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/milesj"}], "dependencies": {}, "description": "TypeScript definitions for yargs-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs-parser", "license": "MIT", "main": "", "name": "@types/yargs-parser", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/yargs-parser"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "692e79ed176a53400e7007c2c3a8ab37780ca4e58b46acaec3e61cd1e365d4d2", "version": "20.2.1"}