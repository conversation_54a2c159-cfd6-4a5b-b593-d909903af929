{"_args": [["@types/source-list-map@0.1.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/source-list-map@0.1.2", "_id": "@types/source-list-map@0.1.2", "_inBundle": false, "_integrity": "sha1-AHiDYGP/rxdBI0m7o2QIfgrALsk=", "_location": "/@types/source-list-map", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/source-list-map@0.1.2", "name": "@types/source-list-map", "escapedName": "@types%2fsource-list-map", "scope": "@types", "rawSpec": "0.1.2", "saveSpec": null, "fetchSpec": "0.1.2"}, "_requiredBy": ["/@types/webpack-sources"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/source-list-map/-/source-list-map-0.1.2.tgz", "_spec": "0.1.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud"}], "dependencies": {}, "description": "TypeScript definitions for source-list-map", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/source-list-map", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.0", "typesPublisherContentHash": "5f6375ac4dc8a29e535bfbe3201cb00cbe1ab108d86918f8fd8aeea32f7ae45f", "version": "0.1.2"}