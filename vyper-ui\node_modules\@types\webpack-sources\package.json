{"_args": [["@types/webpack-sources@3.2.0", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/webpack-sources@3.2.0", "_id": "@types/webpack-sources@3.2.0", "_inBundle": false, "_integrity": "sha1-FtdZuglsKJA0smVT0t8b9FJI04s=", "_location": "/@types/webpack-sources", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/webpack-sources@3.2.0", "name": "@types/webpack-sources", "escapedName": "@types%2fwebpack-sources", "scope": "@types", "rawSpec": "3.2.0", "saveSpec": null, "fetchSpec": "3.2.0"}, "_requiredBy": ["/@types/webpack"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/webpack-sources/-/webpack-sources-3.2.0.tgz", "_spec": "3.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud"}, {"name": "<PERSON>", "url": "https://github.com/chrise<PERSON>tein"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "dependencies": {"@types/node": "*", "@types/source-list-map": "*", "source-map": "^0.7.3"}, "description": "TypeScript definitions for webpack-sources", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-sources", "license": "MIT", "main": "", "name": "@types/webpack-sources", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-sources"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "7e7ca0cf90f1bb589fc4b5ebc534a134ca1068088fc7e58500fc94d5634fbaf1", "version": "3.2.0"}