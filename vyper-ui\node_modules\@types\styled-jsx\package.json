{"_args": [["@types/styled-jsx@2.2.9", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@types/styled-jsx@2.2.9", "_id": "@types/styled-jsx@2.2.9", "_inBundle": false, "_integrity": "sha1-5Qs/howFW8v5vDU+ymwQ/a0ypT8=", "_location": "/@types/styled-jsx", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/styled-jsx@2.2.9", "name": "@types/styled-jsx", "escapedName": "@types%2fstyled-jsx", "scope": "@types", "rawSpec": "2.2.9", "saveSpec": null, "fetchSpec": "2.2.9"}, "_requiredBy": ["/@material-ui/pickers", "/material-table/@material-ui/pickers"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/styled-jsx/-/styled-jsx-2.2.9.tgz", "_spec": "2.2.9", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "R1ZZU", "url": "https://github.com/R1ZZU"}], "dependencies": {"@types/react": "*"}, "description": "TypeScript definitions for styled-jsx", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/styled-jsx", "license": "MIT", "main": "", "name": "@types/styled-jsx", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/styled-jsx"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "cdc4dc14e6e1b1a507bfe8cb8130cfe7582cc6f2ff7924545b9f617c67a7f121", "version": "2.2.9"}