{"_args": [["@types/react@17.0.37", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@types/react@17.0.37", "_id": "@types/react@17.0.37", "_inBundle": false, "_integrity": "sha1-aITQqkAmBZNcOXrmid7tEVyq2Vk=", "_location": "/@types/react", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/react@17.0.37", "name": "@types/react", "escapedName": "@types%2freact", "scope": "@types", "rawSpec": "17.0.37", "saveSpec": null, "fetchSpec": "17.0.37"}, "_requiredBy": ["/@types/hoist-non-react-statics", "/@types/react-redux", "/@types/react-transition-group", "/@types/styled-jsx"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/react/-/react-17.0.37.tgz", "_spec": "17.0.37", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bbenezech"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pza<PERSON><PERSON>ky"}, {"name": "<PERSON>", "url": "https://github.com/ericanderson"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON>", "url": "https://github.com/theruther4d"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/guil<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ferdaber"}, {"name": "<PERSON>", "url": "https://github.com/jrakotoharisoa"}, {"name": "<PERSON>", "url": "https://github.com/pascaloliv"}, {"name": "<PERSON>", "url": "https://github.com/hotell"}, {"name": "<PERSON>", "url": "https://github.com/franklixuefei"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia"}, {"name": "Saransh Kataria", "url": "https://github.com/saranshkataria"}, {"name": "Kanitkorn Sujautra", "url": "https://github.com/lukyth"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/zieka"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/dancerphil"}, {"name": "<PERSON>", "url": "https://github.com/dimitrop<PERSON>los"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/disjukr"}, {"name": "<PERSON>", "url": "https://github.com/vhfmag"}, {"name": "<PERSON>", "url": "https://github.com/hellatan"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/priyanshurav"}], "dependencies": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}, "description": "TypeScript definitions for React", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react", "license": "MIT", "main": "", "name": "@types/react", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react"}, "scripts": {}, "typeScriptVersion": "3.8", "types": "index.d.ts", "typesPublisherContentHash": "91aa69834e615fe0e64009b964243f620503bdaf0c74f0a35b07b7f35b0f8de4", "version": "17.0.37"}