{"_args": [["react-transition-group@4.4.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "react-transition-group@4.4.2", "_id": "react-transition-group@4.4.2", "_inBundle": false, "_integrity": "sha1-i1mlbwnO17VcvVPDZ2i5IokNVHA=", "_location": "/react-transition-group", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "react-transition-group@4.4.2", "name": "react-transition-group", "escapedName": "react-transition-group", "rawSpec": "4.4.2", "saveSpec": null, "fetchSpec": "4.4.2"}, "_requiredBy": ["/@material-ui/core", "/@material-ui/pickers", "/material-table/@material-ui/pickers"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/react-transition-group/-/react-transition-group-4.4.2.tgz", "_spec": "4.4.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "author": "", "browserify": {"transform": ["loose-envify"]}, "bugs": {"url": "https://github.com/reactjs/react-transition-group/issues"}, "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "description": "A react component toolset for managing animations", "homepage": "https://github.com/reactjs/react-transition-group#readme", "jest": {"testRegex": "-test\\.js", "setupFiles": ["./test/setup.js"], "roots": ["<rootDir>/test"]}, "keywords": ["react", "transition", "addons", "transition-group", "animation", "css", "transitions"], "license": "BSD-3-<PERSON><PERSON>", "main": "cjs/index.js", "module": "esm/index.js", "name": "react-transition-group", "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}, "release": {"pkgRoot": "lib", "verifyConditions": ["@semantic-release/changelog", "semantic-release-alt-publish-dir", "@semantic-release/git", "@semantic-release/github"], "prepare": ["@semantic-release/changelog", "semantic-release-alt-publish-dir", "@semantic-release/npm", "@semantic-release/git"]}, "repository": {"type": "git", "url": "git+https://github.com/reactjs/react-transition-group.git"}, "sideEffects": false, "version": "4.4.2"}