{"_args": [["@types/prettier@2.4.2", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/prettier@2.4.2", "_id": "@types/prettier@2.4.2", "_inBundle": false, "_integrity": "sha1-TGL66T60eWYMO9k/nSTVYVl6goE=", "_location": "/@types/prettier", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/prettier@2.4.2", "name": "@types/prettier", "escapedName": "@types%2fprettier", "scope": "@types", "rawSpec": "2.4.2", "saveSpec": null, "fetchSpec": "2.4.2"}, "_requiredBy": ["/jest-snapshot"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/prettier/-/prettier-2.4.2.tgz", "_spec": "2.4.2", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/ikatyang"}, {"name": "<PERSON><PERSON><PERSON> Jr.", "url": "https://github.com/ifiokjr"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ffflorian"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/sosuke<PERSON>zuki"}, {"name": "<PERSON>", "url": "https://github.com/Shinigami92"}, {"name": "<PERSON>", "url": "https://github.com/kddeisz"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/thorn0"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/shian15810"}], "dependencies": {}, "description": "TypeScript definitions for prettier", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/prettier", "license": "MIT", "main": "", "name": "@types/prettier", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/prettier"}, "scripts": {}, "typeScriptVersion": "3.7", "types": "index.d.ts", "typesPublisherContentHash": "fbbf69a42a9af70719f5336d6565fac123fe71715b7b828169d5e5d1dc07de33", "version": "2.4.2"}