{"_args": [["@types/raf@3.4.0", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@types/raf@3.4.0", "_id": "@types/raf@3.4.0", "_inBundle": false, "_integrity": "sha1-K3LL1VQF4HHxxNKZkmOOAisgrMI=", "_location": "/@types/raf", "_optional": true, "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/raf@3.4.0", "name": "@types/raf", "escapedName": "@types%2fraf", "scope": "@types", "rawSpec": "3.4.0", "saveSpec": null, "fetchSpec": "3.4.0"}, "_requiredBy": ["/canvg"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/raf/-/raf-3.4.0.tgz", "_spec": "3.4.0", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/BenLorantfy"}], "dependencies": {}, "description": "TypeScript definitions for raf", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/raf", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.0", "types": "index", "typesPublisherContentHash": "0c3d2cdca7759973a1103c65d7a0f145d0fc503e851040030f272cdc5cb17846", "version": "3.4.0"}