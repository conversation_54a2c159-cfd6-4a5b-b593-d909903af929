{"_args": [["@types/lodash@4.14.177", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_from": "@types/lodash@4.14.177", "_id": "@types/lodash@4.14.177", "_inBundle": false, "_integrity": "sha1-9wwNGcMPqxAcrUa1K+YDY8Q8RXg=", "_location": "/@types/lodash", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/lodash@4.14.177", "name": "@types/lodash", "escapedName": "@types%2flodash", "scope": "@types", "rawSpec": "4.14.177", "saveSpec": null, "fetchSpec": "4.14.177"}, "_requiredBy": ["/yup"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/lodash/-/lodash-4.14.177.tgz", "_spec": "4.14.177", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/bczengel"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chrootsu"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/stepancar"}, {"name": "<PERSON>", "url": "https://github.com/aj-r"}, {"name": "e-cloud", "url": "https://github.com/e-cloud"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/thorn0"}, {"name": "<PERSON>", "url": "https://github.com/jtmthf"}, {"name": "<PERSON>", "url": "https://github.com/DomiR"}, {"name": "<PERSON>", "url": "https://github.com/William<PERSON>"}], "dependencies": {}, "description": "TypeScript definitions for Lo-Dash", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lodash", "license": "MIT", "main": "", "name": "@types/lodash", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/lodash"}, "scripts": {}, "typeScriptVersion": "3.7", "types": "index.d.ts", "typesPublisherContentHash": "62c83f6b673d4d070131f428e31523cd62295c6966c79eada66ab2f7179e9aeb", "version": "4.14.177"}