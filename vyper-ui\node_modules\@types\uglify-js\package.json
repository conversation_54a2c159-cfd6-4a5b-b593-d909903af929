{"_args": [["@types/uglify-js@3.13.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/uglify-js@3.13.1", "_id": "@types/uglify-js@3.13.1", "_inBundle": false, "_integrity": "sha1-XoienoHpQkXHW2RQYA4cXqKHiuo=", "_location": "/@types/uglify-js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/uglify-js@3.13.1", "name": "@types/uglify-js", "escapedName": "@types%2fuglify-js", "scope": "@types", "rawSpec": "3.13.1", "saveSpec": null, "fetchSpec": "3.13.1"}, "_requiredBy": ["/@types/webpack"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/uglify-js/-/uglify-js-3.13.1.tgz", "_spec": "3.13.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/alan-agius4"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "dependencies": {"source-map": "^0.6.1"}, "description": "TypeScript definitions for UglifyJS", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/uglify-js", "license": "MIT", "main": "", "name": "@types/uglify-js", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/uglify-js"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "bf88e1f7f9d4879a56f46a116ac78628ac22d5b1e66bf80ee92ebb562c11613d", "version": "3.13.1"}