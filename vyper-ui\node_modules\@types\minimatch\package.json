{"_args": [["@types/minimatch@3.0.5", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/minimatch@3.0.5", "_id": "@types/minimatch@3.0.5", "_inBundle": false, "_integrity": "sha1-EAHMXmo3BLg8I2An538vWOoBD0A=", "_location": "/@types/minimatch", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/minimatch@3.0.5", "name": "@types/minimatch", "escapedName": "@types%2fminimatch", "scope": "@types", "rawSpec": "3.0.5", "saveSpec": null, "fetchSpec": "3.0.5"}, "_requiredBy": ["/@types/glob", "/multimatch"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/minimatch/-/minimatch-3.0.5.tgz", "_spec": "3.0.5", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "vvakame", "url": "https://github.com/vvakame"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/shantmarouti"}], "dependencies": {}, "description": "TypeScript definitions for Minimatch", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/minimatch", "license": "MIT", "main": "", "name": "@types/minimatch", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/minimatch"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "ce8670ab7ddb0b32136aa0f819c3e7d791e75f04ff991f2f1baa3a9967dd61c0", "version": "3.0.5"}