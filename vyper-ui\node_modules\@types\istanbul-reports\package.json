{"_args": [["@types/istanbul-reports@3.0.1", "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui"]], "_development": true, "_from": "@types/istanbul-reports@3.0.1", "_id": "@types/istanbul-reports@3.0.1", "_inBundle": false, "_integrity": "sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8=", "_location": "/@types/istanbul-reports", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/istanbul-reports@3.0.1", "name": "@types/istanbul-reports", "escapedName": "@types%2fistanbul-reports", "scope": "@types", "rawSpec": "3.0.1", "saveSpec": null, "fetchSpec": "3.0.1"}, "_requiredBy": ["/@jest/types"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz", "_spec": "3.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/jason0x43"}, {"name": "<PERSON>", "url": "https://github.com/not-a-doctor"}], "dependencies": {"@types/istanbul-lib-report": "*"}, "description": "TypeScript definitions for istanbul-reports", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-reports", "license": "MIT", "main": "", "name": "@types/istanbul-reports", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/istanbul-reports"}, "scripts": {}, "typeScriptVersion": "3.6", "types": "index.d.ts", "typesPublisherContentHash": "b331eb26db90bca3bd6f1e18a10a4f37631f149624847439756763800996e143", "version": "3.0.1"}