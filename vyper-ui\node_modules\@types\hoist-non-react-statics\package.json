{"_from": "@types/hoist-non-react-statics@^3.3.0", "_id": "@types/hoist-non-react-statics@3.3.6", "_inBundle": false, "_integrity": "sha512-lPByRJUer/iN/xa4qpyL0qmL11DqNW81iU/IG1S3uvRUq4oKagz8VCxZjiWkumgt66YT3vOdDgZ0o32sGKtCEw==", "_location": "/@types/hoist-non-react-statics", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/hoist-non-react-statics@^3.3.0", "name": "@types/hoist-non-react-statics", "escapedName": "@types%2fhoist-non-react-statics", "scope": "@types", "rawSpec": "^3.3.0", "saveSpec": null, "fetchSpec": "^3.3.0"}, "_requiredBy": ["/@types/react-redux"], "_resolved": "https://artifactory.itg.ti.com/artifactory/api/npm/npm-tmg-release/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.6.tgz", "_shasum": "6bba74383cdab98e8db4e20ce5b4a6b98caed010", "_spec": "@types/hoist-non-react-statics@^3.3.0", "_where": "C:\\Users\\<USER>\\Desktop\\vyper\\vyper-ui\\node_modules\\@types\\react-redux", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "url": "https://github.com/jamesreggio"}], "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}, "deprecated": false, "description": "TypeScript definitions for hoist-non-react-statics", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hoist-non-react-statics", "license": "MIT", "main": "", "name": "@types/hoist-non-react-statics", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/hoist-non-react-statics"}, "scripts": {}, "typeScriptVersion": "5.0", "types": "index.d.ts", "typesPublisherContentHash": "f828fd090493b00844946121848239611dd37ff529ab0738bdbe126f4576476e", "version": "3.3.6"}